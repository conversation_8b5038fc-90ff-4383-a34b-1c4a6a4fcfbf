import pandas as pd

def combine_and_sum_hourly_prices(file1_path, file2_path,
                                 file1_date_column, file1_price_column,
                                 file2_date_column, file2_price_column):
    """
    Combines hourly prices from two Excel files and sums them hour by hour.
    Each file can have different column names.
    """
    try:
        # Load the dataframes
        df1 = pd.read_excel(file1_path)
        df2 = pd.read_excel(file2_path)

        # Debug: Print column names to see what's actually in the files
        print(f"Columns in {file1_path}: {list(df1.columns)}")
        print(f"Columns in {file2_path}: {list(df2.columns)}")
        print(f"Looking for date column in file1: '{file1_date_column}'")
        print(f"Looking for price column in file1: '{file1_price_column}'")
        print(f"Looking for date column in file2: '{file2_date_column}'")
        print(f"Looking for price column in file2: '{file2_price_column}'")

        # Check if columns exist in file1
        if file1_date_column not in df1.columns:
            print(f"Date column '{file1_date_column}' not found in {file1_path}")
            return None
        if file1_price_column not in df1.columns:
            print(f"Price column '{file1_price_column}' not found in {file1_path}")
            return None

        # Check if columns exist in file2
        if file2_date_column not in df2.columns:
            print(f"Date column '{file2_date_column}' not found in {file2_path}")
            return None
        if file2_price_column not in df2.columns:
            print(f"Price column '{file2_price_column}' not found in {file2_path}")
            return None

        # Rename columns for easier access, assuming 'A' and 'B' are column names from Excel.
        # If your Excel actually has headers, adjust these names.
        # Function to clean and parse date strings
        def parse_and_clean_date(date_range_str):
            # Split the string to get the first date part
            first_date_part = str(date_range_str).split(' - ')[0]
            # Remove any trailing timezone information like ' (CET)'
            cleaned_date_str = first_date_part.split(' (')[0]
            # Use flexible parsing to handle different date formats
            return pd.to_datetime(cleaned_date_str, format='mixed')

        # Apply the cleaning and parsing function
        df1['Date'] = df1[file1_date_column].apply(parse_and_clean_date)
        df2['Date'] = df2[file2_date_column].apply(parse_and_clean_date)

        # Set 'Date' as index for easier grouping
        df1.set_index('Date', inplace=True)
        df2.set_index('Date', inplace=True)

        # Keep original hourly frequency - no resampling needed
        hourly_prices1 = df1[file1_price_column]
        hourly_prices2 = df2[file2_price_column]

        # Combine the hourly prices
        combined_hourly_prices = pd.DataFrame({
            'Price_File1': hourly_prices1,
            'Price_File2': hourly_prices2
        }).fillna(0) # Fill NaN with 0 if a date exists in one file but not the other

        # Sum the prices from both files for each hour
        combined_hourly_prices['Combined_Price'] = combined_hourly_prices['Price_File1'] + combined_hourly_prices['Price_File2']

        # Reset index to make 'Date' a regular column again
        combined_hourly_prices.reset_index(inplace=True)

        print("Combined Hourly Prices (first 5 rows):")
        print(combined_hourly_prices.head()) # Display the first few rows
        print("\nSummary of Combined Data:")
        combined_hourly_prices.info()

        return combined_hourly_prices

    except FileNotFoundError as e:
        print(f"Error: One of the files was not found. {e}")
        return None
    except KeyError as e:
        print(f"Error: Missing expected column. Please check 'date_column' and 'price_column' arguments. {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

# --- Main execution ---
if __name__ == "__main__":
    file1 = 'BE+2017-2020.xlsx'
    file2 = 'GB+2017-2020.xlsx'

    # Use a mapping approach for different column names
    be_date_col = 'MTU (CET/CEST)'
    be_price_col = 'Day-ahead Price (EUR/MWh)'
    
    gb_date_col = 'Time'
    gb_price_col = 'price'
    
    # Call the function with different column names for each file
    combined_data = combine_and_sum_hourly_prices(
        file1, file2,
        file1_date_column=be_date_col, file1_price_column=be_price_col,
        file2_date_column=gb_date_col, file2_price_column=gb_price_col
    )

    if combined_data is not None:
        # You can now further process or save combined_data
        # For example, save to a new Excel file:
        combined_data.to_excel("combined_hourly_prices.xlsx", index=False)
        # print("\nCombined data saved to 'combined_hourly_prices.xlsx'")
        pass
